{"$schema": "https://wixplosives.github.io/codux-config-schema/codux.config.schema.json", "boardGlobalSetup": "./src/_codux/boards-global-setup.ts", "componentsDiscovery": {"include": ["src/**"], "exclude": ["src/_codux/component-templates/**"]}, "newComponent": {"componentsPath": "src/components", "templatesPath": "src/_codux/component-templates"}, "safeRender": {"maxInstancesPerComponent": 1000}, "scripts": {"install": {"title": "Install", "description": "Run install", "command": "npm i", "trigger": ["checkout", "pull", "setup"]}}, "styling": {"solution": "css"}, "svgLoader": "both"}